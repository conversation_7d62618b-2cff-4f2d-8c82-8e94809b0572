<?php
declare(strict_types=1);

namespace Lf\Franchise\Setup\Patch\Schema;

use Magento\Framework\Setup\Patch\SchemaPatchInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\DB\Adapter\AdapterInterface;

/**
 * Patch to add franchise_id indexes for performance optimization
 * 
 * This patch addresses the performance issues identified in March 2025
 * when loading sales > orders page was very slow for all franchises,
 * even new ones with few orders.
 */
class AddFranchiseIndexes implements SchemaPatchInterface
{
    private SchemaSetupInterface $schemaSetup;

    public function __construct(SchemaSetupInterface $schemaSetup)
    {
        $this->schemaSetup = $schemaSetup;
    }

    /**
     * @inheritdoc
     */
    public function apply()
    {
        $this->schemaSetup->startSetup();
        $connection = $this->schemaSetup->getConnection();

        // 1. Index on sales_order.franchise_id (if not exists)
        $this->addIndexIfNotExists(
            'sales_order',
            'franchise_id',
            'IDX_SALES_ORDER_FRANCHISE_ID'
        );

        // 2. Index on sales_order_grid.franchise_id (if not exists)
        $this->addIndexIfNotExists(
            'sales_order_grid',
            'franchise_id',
            'IDX_SALES_ORDER_GRID_FRANCHISE_ID'
        );

        // 3. Index on sales_invoice.order_id for JOIN performance with sales_order
        $this->addIndexIfNotExists(
            'sales_invoice',
            'order_id',
            'IDX_SALES_INVOICE_ORDER_ID'
        );

        // 4. Index on sales_creditmemo.order_id for JOIN performance with sales_order
        $this->addIndexIfNotExists(
            'sales_creditmemo',
            'order_id',
            'IDX_SALES_CREDITMEMO_ORDER_ID'
        );

        // 5. Index on customer_entity_text for franchise filtering
        // This helps with the FIND_IN_SET queries on franchises_lies attribute
        $this->addIndexIfNotExists(
            'customer_entity_text',
            ['entity_id', 'attribute_id'],
            'IDX_CUSTOMER_ENTITY_TEXT_ENTITY_ATTR'
        );

        // 6. Index on franchise_product.franchise_id (if not exists)
        $this->addIndexIfNotExists(
            'franchise_product',
            'franchise_id',
            'IDX_FRANCHISE_PRODUCT_FRANCHISE_ID'
        );

        // 7. Index on partenaire.franchise_id (if not exists)
        $this->addIndexIfNotExists(
            'partenaire',
            'franchise_id',
            'IDX_PARTENAIRE_FRANCHISE_ID'
        );

        // 8. Index on eatlf_partenaire_contribution.partenaire_id for BAP filtering
        $this->addIndexIfNotExists(
            'eatlf_partenaire_contribution',
            'partenaire_id',
            'IDX_PARTENAIRE_CONTRIBUTION_PARTENAIRE_ID'
        );

        // 9. Index on eatlf_bap.contribution_id for BAP filtering
        $this->addIndexIfNotExists(
            'eatlf_bap',
            'contribution_id',
            'IDX_EATLF_BAP_CONTRIBUTION_ID'
        );

        // 10. Composite index on sales_invoice_grid for better performance
        $this->addIndexIfNotExists(
            'sales_invoice_grid',
            'order_id',
            'IDX_SALES_INVOICE_GRID_ORDER_ID'
        );

        // 11. Composite index on sales_creditmemo_grid for better performance
        $this->addIndexIfNotExists(
            'sales_creditmemo_grid',
            'order_id',
            'IDX_SALES_CREDITMEMO_GRID_ORDER_ID'
        );

        // 12. Index on quote_shipping_datas.franchise_id (if exists)
        if ($connection->tableColumnExists('quote_shipping_datas', 'franchise_id')) {
            $this->addIndexIfNotExists(
                'quote_shipping_datas',
                'franchise_id',
                'IDX_QUOTE_SHIPPING_DATAS_FRANCHISE_ID'
            );
        }

        $this->schemaSetup->endSetup();

        return $this;
    }

    /**
     * Add index if it doesn't already exist
     *
     * @param string $tableName
     * @param string|array $columns
     * @param string $indexName
     * @return void
     */
    private function addIndexIfNotExists(string $tableName, $columns, string $indexName): void
    {
        $connection = $this->schemaSetup->getConnection();
        $table = $this->schemaSetup->getTable($tableName);

        // Check if table exists
        if (!$connection->isTableExists($table)) {
            return;
        }

        // Check if index already exists
        $indexes = $connection->getIndexList($table);
        if (isset($indexes[$indexName])) {
            return;
        }

        // Ensure columns exist
        $columns = is_array($columns) ? $columns : [$columns];
        foreach ($columns as $column) {
            if (!$connection->tableColumnExists($table, $column)) {
                return;
            }
        }

        // Add the index
        try {
            $connection->addIndex(
                $table,
                $indexName,
                $columns,
                AdapterInterface::INDEX_TYPE_INDEX
            );
        } catch (\Exception $e) {
            // Log error but don't fail the patch
            // This allows the patch to continue even if one index fails
        }
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function getAliases(): array
    {
        return [];
    }
}
