<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

     <type name="Lf\Devis\Model\Pdf\Output\Email">
        <plugin sortOrder="1" name="lfFranchiseEmail" type="Lf\Franchise\Plugin\Model\Pdf\Output\EmailPlugin"/>
    </type>

    <virtualType name="Lf\Franchise\Ui\DataProvider\Modifier\Pool" type="Magento\Ui\DataProvider\Modifier\Pool">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="lf_product" xsi:type="array">
                    <item name="class" xsi:type="string">Lf\Franchise\Ui\DataProvider\Product\Form\Modifier\UpdateForm</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Lf\Franchise\Model\Franchise\Product\DataProvider">
        <arguments>
            <argument name="pool" xsi:type="object">Lf\Franchise\Ui\DataProvider\Modifier\Pool</argument>
        </arguments>
    </type>

    <!--DeliveryMAn-->
    <preference for="Lf\Franchise\Api\DeliveryManRepositoryInterface" type="Lf\Franchise\Model\DeliveryManRepository" />
    <preference for="Lf\Franchise\Api\Data\DeliveryManInterface" type="Lf\Franchise\Model\DeliveryMan" />
    <preference for="Lf\Franchise\Api\Data\DeliveryManSearchResultsInterface" type="Magento\Framework\Api\SearchResults" />
    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\DeliveryManFilterProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor">
        <arguments>
            <argument name="customFilters" xsi:type="array">
                <item name="franchise_id" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\FilterProcessor\DeliveryManFranchiseFilter</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\DeliveryManCollectionProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="filters" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\DeliveryManFilterProcessor</item>
                <item name="sorting" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\SortingProcessor</item>
                <item name="pagination" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\PaginationProcessor</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Lf\Franchise\Model\DeliveryManRepository">
        <arguments>
            <argument name="collectionProcessor" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\DeliveryManCollectionProcessor</argument>
        </arguments>
    </type>

    <!--Timeslot-->
    <preference for="Lf\Franchise\Api\TimeslotManagementInterface" type="Lf\Franchise\Model\TimeslotManagement" />
    <preference for="Lf\Franchise\Api\TimeslotRepositoryInterface" type="Lf\Franchise\Model\TimeslotRepository" />
    <preference for="Lf\Franchise\Api\Data\TimeslotInterface" type="Lf\Franchise\Model\Timeslot" />
    <preference for="Lf\Franchise\Api\Data\TimeslotSearchResultsInterface" type="Magento\Framework\Api\SearchResults" />
    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\TimeslotFilterProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor">
        <arguments>
            <argument name="customFilters" xsi:type="array">
                <item name="franchise_id" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\FilterProcessor\DeliveryManFranchiseFilter</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\TimeslotCollectionProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="filters" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\TimeslotFilterProcessor</item>
                <item name="sorting" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\SortingProcessor</item>
                <item name="pagination" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\PaginationProcessor</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Lf\Franchise\Model\TimeslotRepository">
        <arguments>
            <argument name="collectionProcessor" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\TimeslotCollectionProcessor</argument>
        </arguments>
    </type>


    <!--Holidays-->
    <preference for="Lf\Franchise\Api\HolidaysRepositoryInterface" type="Lf\Franchise\Model\HolidaysRepository" />
    <preference for="Lf\Franchise\Api\Data\HolidaysInterface" type="Lf\Franchise\Model\Holidays" />
    <preference for="Lf\Franchise\Api\Data\HolidaysSearchResultsInterface" type="Magento\Framework\Api\SearchResults" />
    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\HolidaysFilterProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor">
        <arguments>
            <argument name="customFilters" xsi:type="array">
                <item name="franchise_id" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\FilterProcessor\DeliveryManFranchiseFilter</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\HolidaysCollectionProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="filters" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\HolidaysFilterProcessor</item>
                <item name="sorting" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\SortingProcessor</item>
                <item name="pagination" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\PaginationProcessor</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Lf\Franchise\Model\HolidaysRepository">
        <arguments>
            <argument name="collectionProcessor" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\HolidaysCollectionProcessor</argument>
        </arguments>
    </type>
    <virtualType name="Lf\Franchise\DataProvider\Modifier\Pool" type="Magento\Ui\DataProvider\Modifier\Pool">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="lf_holidays" xsi:type="array">
                    <item name="class" xsi:type="string">Lf\Franchise\Ui\DataProvider\Holidays\Form\Modifier\UpdateHolidaysForm</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Lf\Franchise\Model\Holidays\DataProvider">
        <arguments>
            <argument name="pool" xsi:type="object">Lf\Franchise\DataProvider\Modifier\Pool</argument>
        </arguments>
    </type>

    <!--Franchise-->
    <preference for="Lf\Franchise\Api\FranchiseRepositoryInterface" type="Lf\Franchise\Model\ResourceModel\FranchiseRepository" />
    <preference for="Lf\Franchise\Api\Data\FranchiseInterface" type="Lf\Franchise\Model\Franchise" />
    <preference for="Lf\Franchise\Api\Data\FranchiseSearchResultsInterface" type="Magento\Framework\Api\SearchResults" />

    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\FranchiseCollectionProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="sorting" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\SortingProcessor</item>
                <item name="pagination" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\PaginationProcessor</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Lf\Franchise\Model\ResourceModel\FranchiseRepository">
        <arguments>
            <argument name="collectionProcessor" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\FranchiseCollectionProcessor</argument>
        </arguments>
    </type>


    <!--Zone-->
    <preference for="Lf\Franchise\Api\ZoneRepositoryInterface" type="Lf\Franchise\Model\ZoneRepository" />
    <preference for="Lf\Franchise\Api\Data\ZoneInterface" type="Lf\Franchise\Model\Zone" />
    <preference for="Lf\Franchise\Api\Data\ZoneSearchResultsInterface" type="Magento\Framework\Api\SearchResults" />

    <!--Grid : Franchise/listing-->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="franchise_listing_data_source" xsi:type="string">Lf\Franchise\Model\ResourceModel\Franchise\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="Lf\Franchise\Model\ResourceModel\Franchise\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">franchise</argument>
            <argument name="resourceModel" xsi:type="string">Lf\Franchise\Model\ResourceModel\Franchise</argument>
        </arguments>
    </virtualType>

    <!--Grid : Zone/listing-->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="zone_listing_data_source" xsi:type="string">Lf\Franchise\Model\ResourceModel\Zone\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Lf\Franchise\Model\ResourceModel\Zone\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">zone</argument>
            <argument name="eventPrefix" xsi:type="string">zone_collection</argument>
            <argument name="eventObject" xsi:type="string">zone_grid_collection</argument>
            <argument name="resourceModel" xsi:type="string">Lf\Franchise\Model\ResourceModel\Zone</argument>
        </arguments>
    </type>

    <!--Grid : Zone/attribute -->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="zone_attribute_data_source" xsi:type="string">Lf\Franchise\Model\ResourceModel\ZoneShippingAttributeSet\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Lf\Franchise\Model\ResourceModel\ZoneShippingAttributeSet\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">zone_shipping_attribute_set</argument>
            <argument name="eventPrefix" xsi:type="string">zone_shipping_attribute_collection</argument>
            <argument name="eventObject" xsi:type="string">zone_grid_collection</argument>
            <argument name="resourceModel" xsi:type="string">Lf\Franchise\Model\ResourceModel\ZoneShippingAttributeSet</argument>
        </arguments>
    </type>

    <!--Grid : Zone/timeslot-->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="zone_timeslot_data_source" xsi:type="string">Lf\Franchise\Model\ResourceModel\Timeslot\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Lf\Franchise\Model\ResourceModel\Timeslot\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">timeslot_grid</argument>
            <argument name="eventPrefix" xsi:type="string">zone_timeslot_collection</argument>
            <argument name="eventObject" xsi:type="string">zone_grid_collection</argument>
            <argument name="resourceModel" xsi:type="string">Lf\Franchise\Model\ResourceModel\Timeslot</argument>
        </arguments>
    </type>

    <!--Grid : Franchise/product-->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="franchise_product_data_source" xsi:type="string">Lf\Franchise\Model\ResourceModel\FranchiseProduct\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <preference for="Lf\Franchise\Api\FranchiseProductRepositoryInterface"
                type="Lf\Franchise\Model\FranchiseProductRepository" />

    <preference for="Lf\Franchise\Api\Data\FranchiseProductInterface"
                type="Lf\Franchise\Model\FranchiseProduct" />

    <preference for="Lf\Franchise\Api\Data\FranchiseProductSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults" />

    <!--Grid : Franchise/deliveryman -->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="franchise_deliveryman_data_source" xsi:type="string">Lf\Franchise\Model\ResourceModel\DeliveryMan\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Lf\Franchise\Model\ResourceModel\DeliveryMan\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">delivery_man</argument>
            <argument name="eventPrefix" xsi:type="string">franchise_deliveryman_collection</argument>
            <argument name="eventObject" xsi:type="string">franchise_grid_collection</argument>
            <argument name="resourceModel" xsi:type="string">Lf\Franchise\Model\ResourceModel\DeliveryMan</argument>
        </arguments>
    </type>

    <!-- Closed Timeslot-->
    <preference for="Lf\Franchise\Api\ClosedTimeslotRepositoryInterface" type="Lf\Franchise\Model\Timeslot\ClosedRepository" />
    <preference for="Lf\Franchise\Api\Data\ClosedTimeslotInterface" type="Lf\Franchise\Model\Timeslot\Closed" />
    <preference for="Lf\Franchise\Api\Data\ClosedTimeslotSearchResultsInterface" type="Magento\Framework\Api\SearchResults" />

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="closed_timeslot_data_source" xsi:type="string">Lf\Franchise\Model\ResourceModel\Timeslot\Closed\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <type name="Lf\Franchise\Model\ResourceModel\Timeslot\Closed\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">closed_timeslot_grid</argument>
            <argument name="eventPrefix" xsi:type="string">closed_timeslot_collection</argument>
            <argument name="eventObject" xsi:type="string">closed_timeslot_grid_collection</argument>
            <argument name="resourceModel" xsi:type="string">Lf\Franchise\Model\ResourceModel\Timeslot\Closed</argument>
        </arguments>
    </type>
    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\ClosedTimeslotFilterProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor">
        <arguments>
            <argument name="customFilters" xsi:type="array">
                <item name="franchise_id" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\FilterProcessor\DeliveryManFranchiseFilter</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\ClosedTimeslotCollectionProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="filters" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\ClosedTimeslotFilterProcessor</item>
                <item name="sorting" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\SortingProcessor</item>
                <item name="pagination" xsi:type="object">Magento\Framework\Api\SearchCriteria\CollectionProcessor\PaginationProcessor</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Lf\Franchise\Model\Timeslot\ClosedRepository">
        <arguments>
            <argument name="collectionProcessor" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\ClosedTimeslotCollectionProcessor</argument>
        </arguments>
    </type>

    <virtualType name="Lf\Franchise\Model\ResourceModel\Timeslot\Closed\Grid" type="Lf\Franchise\Model\ResourceModel\ClosedTimeslotGrid">
        <arguments>
            <argument name="mainTableName" xsi:type="string">closed_timeslot</argument>
            <argument name="gridTableName" xsi:type="string">closed_timeslot_grid</argument>
            <argument name="timeslotIdField" xsi:type="string">closed_timeslot.closed_timeslot_id</argument>
            <argument name="joins" xsi:type="array">
                <item name="timeslot" xsi:type="array">
                    <item name="table" xsi:type="string">timeslot</item>
                    <item name="origin_column" xsi:type="string">timeslot_id</item>
                    <item name="target_column" xsi:type="string">timeslot_id</item>
                </item>
            </argument>
            <argument name="columns" xsi:type="array">
                <item name="closed_timeslot_id" xsi:type="string">closed_timeslot.closed_timeslot_id</item>
                <item name="timeslot_id" xsi:type="string">timeslot.timeslot_id</item>
                <item name="start_hour" xsi:type="string">timeslot.start_hour</item>
                <item name="end_hour" xsi:type="string">timeslot.end_hour</item>
                <item name="zone_code" xsi:type="string">zone.code</item>
                <item name="zone_id" xsi:type="string">zone.zone_id</item>
                <item name="date" xsi:type="string">date</item>
            </argument>
        </arguments>
    </virtualType>

    <preference for="Lf\Franchise\Model\ResourceModel\ClosedTimeslotGridInterface" type="Lf\Franchise\Model\ResourceModel\Timeslot\Closed\Grid" />

    <type name="Lf\Franchise\Observer\RefreshClosedTimeslotGrid">
        <arguments>
            <argument xsi:type="object" name="entityGrid">Lf\Franchise\Model\ResourceModel\Timeslot\Closed\Grid</argument>
        </arguments>
    </type>
    <type name="Lf\Franchise\Observer\RefreshClosedTimeslotGridByZone">
        <arguments>
            <argument xsi:type="object" name="entityGrid">Lf\Franchise\Model\ResourceModel\Timeslot\Closed\Grid</argument>
        </arguments>
    </type>

    <!--Grid : Franchise/holidays -->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="franchise_holidays_data_source" xsi:type="string">Lf\Franchise\Model\ResourceModel\Holidays\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Lf\Franchise\Model\ResourceModel\Holidays\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">holidays</argument>
            <argument name="eventPrefix" xsi:type="string">franchise_holidays_collection</argument>
            <argument name="eventObject" xsi:type="string">franchise_grid_collection</argument>
            <argument name="resourceModel" xsi:type="string">Lf\Franchise\Model\ResourceModel\Holidays</argument>
        </arguments>
    </type>

    <preference for="Magento\User\Block\User\Edit\Tab\Main" type="Lf\Franchise\Block\User\Edit\Tab\Main"></preference>

    <preference for="Magento\SalesSequence\Model\Builder" type="Lf\Franchise\Model\Sequence\Builder"></preference>

    <preference for="Magento\SalesSequence\Model\ResourceModel\Meta" type="Lf\Franchise\Model\ResourceModel\Override\Sequence\Meta"></preference>

    <preference for="Magento\SalesSequence\Model\Manager" type="Lf\Franchise\Model\Sequence\Manager"></preference>

    <type name="Magento\Quote\Model\QuoteManagement">
        <plugin sortOrder="1" name="lfFranchiseQuoteManagement" type="Lf\Franchise\Plugin\Model\QuoteManagementPlugin"/>
    </type>

    <type name="Lf\Franchise\Cron\SetColisNumber">
        <arguments>
            <argument name="orderRepository" xsi:type="object">colis_orders</argument>
        </arguments>
    </type>

    <virtualType name="colis_orders" type="Magento\Sales\Model\OrderRepository">
        <arguments>
            <argument name="collectionProcessor" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\OrderCollectionProcessor</argument>
        </arguments>
    </virtualType>

    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\OrderCollectionProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="joins" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\OrderJoinProcessor</item>
                <item name="filters" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\OrderFilterProcessor</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\OrderJoinProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor\JoinProcessor">
        <arguments>
            <argument name="customJoins" xsi:type="array">
                <item name="shipping_data.shipping_date" xsi:type="object">Lf\Franchise\Model\Api\SearchCriteria\JoinProcessor\ShippingData</item>
            </argument>
            <argument name="fieldMapping" xsi:type="array">
                <item name="shipping_date" xsi:type="string">shipping_datas.shipping_date</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="Lf\Franchise\Model\Api\SearchCriteria\CollectionProcessor\OrderFilterProcessor" type="Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor">
        <arguments>
            <argument name="fieldMapping" xsi:type="array">
                <item name="shipping_date" xsi:type="string">shipping_datas.shipping_date</item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Magento\Email\Model\Template\SenderResolver">
        <plugin sortOrder="1" name="lfFranchiseSenderResolver"
                type="Lf\Franchise\Plugin\Model\Template\SenderResolverPlugin"/>
    </type>

    <!--Timeslot Grid -->
    <virtualType name="Lf\Franchise\Model\ResourceModel\Timeslot\Grid" type="Lf\Franchise\Model\ResourceModel\TimeslotGrid">
        <arguments>
            <argument name="mainTableName" xsi:type="string">timeslot</argument>
            <argument name="gridTableName" xsi:type="string">timeslot_grid</argument>
            <argument name="timeslotIdField" xsi:type="string">timeslot.timeslot_id</argument>
            <argument name="joins" xsi:type="array">
                <item name="zone" xsi:type="array">
                    <item name="table" xsi:type="string">zone</item>
                    <item name="origin_column" xsi:type="string">zone_id</item>
                    <item name="target_column" xsi:type="string">zone_id</item>
                </item>
                <item name="timeslot_type" xsi:type="array">
                    <item name="table" xsi:type="string">timeslot_type</item>
                    <item name="origin_column" xsi:type="string">timeslot_type_id</item>
                    <item name="target_column" xsi:type="string">timeslot_type_id</item>
                </item>
            </argument>
            <argument name="columns" xsi:type="array">
                <item name="timeslot_id" xsi:type="string">timeslot.timeslot_id</item>
                <item name="timeslot_type_name" xsi:type="string">timeslot_type.name</item>
                <item name="is_enable" xsi:type="string">timeslot.is_enable</item>
                <item name="start_hour" xsi:type="string">timeslot.start_hour</item>
                <item name="end_hour" xsi:type="string">timeslot.end_hour</item>
                <item name="is_retention" xsi:type="string">timeslot.is_retention</item>
                <item name="zone_code" xsi:type="string">zone.code</item>
                <item name="zone_id" xsi:type="string">zone.zone_id</item>
                <item name="franchise_id" xsi:type="string">zone.franchise_id</item>
                <item name="timeslot_type_id" xsi:type="string">timeslot.timeslot_type_id</item>
                <item name="preparation_time" xsi:type="string">timeslot.preparation_time</item>

            </argument>
        </arguments>
    </virtualType>

    <preference for="Lf\Franchise\Model\ResourceModel\TimeslotGridInterface" type="Lf\Franchise\Model\ResourceModel\Timeslot\Grid" />

    <type name="Lf\Franchise\Observer\RefreshTimeslotGrid">
        <arguments>
            <argument xsi:type="object" name="entityGrid">Lf\Franchise\Model\ResourceModel\Timeslot\Grid</argument>
        </arguments>
    </type>

    <!--Franchise_product -->
    <virtualType name="Lf\Franchise\Model\ResourceModel\Product\Grid" type="Lf\Franchise\Model\ResourceModel\Grid">
        <arguments>
            <argument name="mainTableName" xsi:type="string">franchise_product</argument>
            <argument name="gridTableName" xsi:type="string">franchise_product_grid</argument>
            <argument name="franchiseProductIdField" xsi:type="string">franchise_product.franchise_product_id</argument>
            <argument name="joins" xsi:type="array">
                <item name="catalog_product_entity" xsi:type="array">
                    <item name="table" xsi:type="string">catalog_product_entity</item>
                    <item name="origin_column" xsi:type="string">product_id</item>
                    <item name="target_column" xsi:type="string">entity_id</item>
                </item>
               <item name="franchise" xsi:type="array">
                   <item name="table" xsi:type="string">franchise</item>
                   <item name="origin_column" xsi:type="string">franchise_id</item>
                   <item name="target_column" xsi:type="string">franchise_id</item>
               </item>
            </argument>
            <argument name="columns" xsi:type="array">
                <item name="franchise_product_id" xsi:type="string">franchise_product.franchise_product_id</item>
                <item name="product_id" xsi:type="string">franchise_product.product_id</item>
                <item name="franchise_id" xsi:type="string">franchise_product.franchise_id</item>
                <item name="sku" xsi:type="string">catalog_product_entity.sku</item>
                <item name="franchise_code" xsi:type="string">franchise.code</item>
                <item name="franchise_price_ttc" xsi:type="string">franchise_product.price</item>
                <item name="franchise_price_ht" xsi:type="string">franchise_product.price_ht</item>
                <item name="franchise_special_price_ttc" xsi:type="string">franchise_product.special_price</item>
                <item name="franchise_special_price_ht" xsi:type="string">franchise_product.special_price_ht</item>
                <item name="franchise_name" xsi:type="string">franchise.name</item>
                <item name="product_name" xsi:type="string">productName.value</item>
                <item name="product_cout_matiere" xsi:type="string">productCoutMatiere.value</item>
                <item name="product_cout_matiere_percent" xsi:type="string">productCoutMatierePercent.value</item>
                <item name="tax_class_name" xsi:type="string">taxClass.class_name</item>
                <item name="tax_class_id" xsi:type="string">franchise_product.tax_class_id</item>
                <item name="code_compta" xsi:type="string">franchise_product.code_compta</item>
                <item name="actif_fo" xsi:type="string">franchise_product.is_active_fo</item>
                <item name="actif_bo" xsi:type="string">franchise_product.is_active_bo</item>
                <item name="franchise_atelier" xsi:type="string">franchise_product.atelier</item>
                <item name="type_id" xsi:type="string">catalog_product_entity.type_id</item>
                <item name="no_production" xsi:type="string">no_production.value</item>
                <item name="franchise_recommanded_ht_price" xsi:type="string">price.value</item>
                <item name="is_accessory" xsi:type="string">is_accessory.value</item>
                <item name="preparation_time" xsi:type="string">franchise_product.preparation_time</item>
                <item name="franchise_recommanded_ttc_price" xsi:type="string">price.value + (taxCalculationRate.rate * price.value / 100)</item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Lf\Franchise\Observer\RefreshFranchiseProductGrid">
        <arguments>
            <argument xsi:type="object" name="entityGrid">Lf\Franchise\Model\ResourceModel\Product\Grid</argument>
        </arguments>
    </type>
    <type name="Lf\Franchise\Observer\RefreshFranchiseProductGridByProduct">
        <arguments>
            <argument xsi:type="object" name="entityGrid">Lf\Franchise\Model\ResourceModel\Product\Grid</argument>
        </arguments>
    </type>

    <virtualType name="Lf\Franchise\Zone\DataProvider\Modifier\Pool" type="Magento\Ui\DataProvider\Modifier\Pool">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="lf_zone" xsi:type="array">
                    <item name="class" xsi:type="string">Lf\Franchise\Ui\DataProvider\Zone\Form\Modifier\UpdateZoneForm</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Lf\Franchise\Observer\RefreshTimeslotGridByZone">
        <arguments>
            <argument xsi:type="object" name="entityGrid">Lf\Franchise\Model\ResourceModel\Timeslot\Grid</argument>
        </arguments>
    </type>

    <preference for="Lf\Franchise\Model\ResourceModel\TimeslotTypeGridInterface" type="Lf\Franchise\Model\ResourceModel\TimeslotTypeGrid" />

    <type name="Lf\Franchise\Observer\RefreshTimeslotGridByTimeslotType">
        <arguments>
            <argument xsi:type="object" name="entityGrid">Lf\Franchise\Model\ResourceModel\Timeslot\Grid</argument>
        </arguments>
    </type>


    <type name="Lf\Franchise\Model\Zone\DataProvider">
        <arguments>
            <argument name="pool" xsi:type="object">Lf\Franchise\Zone\DataProvider\Modifier\Pool</argument>
        </arguments>
    </type>

    <type name="Lf\Franchise\Model\Rule\Condition\Franchise" >
        <arguments>
            <argument name="data" xsi:type="array">
                <item name="form_name" xsi:type="string">sales_rule_form</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="promo_listing_data_source" xsi:type="string">Lf\SalesRule\Model\ResourceModel\SalesRule\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Lf\Franchise\Model\ResourceModel\Zone\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">salesrule</argument>
            <argument name="eventPrefix" xsi:type="string">salesrule_collection</argument>
            <argument name="eventObject" xsi:type="string">salesrule_grid_collection</argument>
            <argument name="resourceModel" xsi:type="string">Lf\SalesRule\Model\ResourceModel\SalesRule</argument>
        </arguments>
    </type>

    <preference for="Lf\Franchise\Model\Franchise\Product\TaxClassUpdaterInterface"
                type="Lf\Franchise\Model\Franchise\Product\TaxClassUpdater"/>

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="lf_refresh_franchise_product_grid" xsi:type="object">
                    Lf\Franchise\Console\Command\RefreshFranchiseProductGrid
                </item>
                <item name="duplicate_franchise_product_data" xsi:type="object">
                    Lf\Franchise\Console\Command\DuplicateFranchiseProductData
                </item>
                <item name="lf_check_franchise_indexes" xsi:type="object">
                    Lf\Franchise\Console\Command\CheckFranchiseIndexes
                </item>
                <item name="lf_test_franchise_performance" xsi:type="object">
                    Lf\Franchise\Console\Command\TestFranchisePerformance
                </item>
            </argument>
        </arguments>
    </type>
</config>
