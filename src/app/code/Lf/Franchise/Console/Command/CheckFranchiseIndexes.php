<?php
declare(strict_types=1);

namespace Lf\Franchise\Console\Command;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\Table;

/**
 * Console command to check franchise-related indexes
 */
class CheckFranchiseIndexes extends Command
{
    private ResourceConnection $resourceConnection;

    /**
     * Expected indexes for franchise performance optimization
     */
    private array $expectedIndexes = [
        'sales_order' => ['IDX_SALES_ORDER_FRANCHISE_ID'],
        'sales_order_grid' => ['IDX_SALES_ORDER_GRID_FRANCHISE_ID'],
        'sales_invoice' => ['IDX_SALES_INVOICE_ORDER_ID'],
        'sales_creditmemo' => ['IDX_SALES_CREDITMEMO_ORDER_ID'],
        'sales_invoice_grid' => ['IDX_SALES_INVOICE_GRID_ORDER_ID'],
        'sales_creditmemo_grid' => ['IDX_SALES_CREDITMEMO_GRID_ORDER_ID'],
        'customer_entity_text' => ['IDX_CUSTOMER_ENTITY_TEXT_ENTITY_ATTR'],
        'franchise_product' => ['IDX_FRANCHISE_PRODUCT_FRANCHISE_ID'],
        'partenaire' => ['IDX_PARTENAIRE_FRANCHISE_ID'],
        'eatlf_partenaire_contribution' => ['IDX_PARTENAIRE_CONTRIBUTION_PARTENAIRE_ID'],
        'eatlf_bap' => ['IDX_EATLF_BAP_CONTRIBUTION_ID'],
    ];

    public function __construct(ResourceConnection $resourceConnection)
    {
        $this->resourceConnection = $resourceConnection;
        parent::__construct();
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName('lf:franchise:check-indexes')
            ->setDescription('Check if franchise performance indexes are properly created');
    }

    /**
     * @inheritdoc
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Checking franchise performance indexes...</info>');
        
        $connection = $this->resourceConnection->getConnection();
        $results = [];
        $allGood = true;

        foreach ($this->expectedIndexes as $tableName => $expectedIndexes) {
            $fullTableName = $this->resourceConnection->getTableName($tableName);
            
            // Check if table exists
            if (!$connection->isTableExists($fullTableName)) {
                $results[] = [
                    'table' => $tableName,
                    'index' => 'N/A',
                    'status' => '<error>TABLE NOT FOUND</error>',
                    'columns' => 'N/A'
                ];
                $allGood = false;
                continue;
            }

            // Get existing indexes
            $existingIndexes = $connection->getIndexList($fullTableName);
            
            foreach ($expectedIndexes as $expectedIndex) {
                if (isset($existingIndexes[$expectedIndex])) {
                    $indexInfo = $existingIndexes[$expectedIndex];
                    $columns = implode(', ', array_keys($indexInfo['COLUMNS_LIST']));
                    $results[] = [
                        'table' => $tableName,
                        'index' => $expectedIndex,
                        'status' => '<info>✓ EXISTS</info>',
                        'columns' => $columns
                    ];
                } else {
                    $results[] = [
                        'table' => $tableName,
                        'index' => $expectedIndex,
                        'status' => '<error>✗ MISSING</error>',
                        'columns' => 'N/A'
                    ];
                    $allGood = false;
                }
            }
        }

        // Display results in a table
        $table = new Table($output);
        $table->setHeaders(['Table', 'Index', 'Status', 'Columns']);
        $table->setRows($results);
        $table->render();

        if ($allGood) {
            $output->writeln('<info>✓ All franchise performance indexes are properly created!</info>');
            return Cli::RETURN_SUCCESS;
        } else {
            $output->writeln('<error>✗ Some indexes are missing. Run "php bin/magento setup:upgrade" to create them.</error>');
            return Cli::RETURN_FAILURE;
        }
    }

    /**
     * Get performance statistics for franchise-related queries
     */
    private function getPerformanceStats(OutputInterface $output): void
    {
        $connection = $this->resourceConnection->getConnection();
        
        $output->writeln('<info>Performance statistics:</info>');
        
        // Example: Count orders by franchise
        try {
            $salesOrderTable = $this->resourceConnection->getTableName('sales_order');
            if ($connection->isTableExists($salesOrderTable)) {
                $select = $connection->select()
                    ->from($salesOrderTable, ['franchise_id', 'COUNT(*) as order_count'])
                    ->group('franchise_id')
                    ->limit(5);
                
                $results = $connection->fetchAll($select);
                
                if (!empty($results)) {
                    $output->writeln('Sample order counts by franchise:');
                    foreach ($results as $result) {
                        $output->writeln(sprintf(
                            '  Franchise %s: %d orders',
                            $result['franchise_id'],
                            $result['order_count']
                        ));
                    }
                }
            }
        } catch (\Exception $e) {
            $output->writeln('<comment>Could not retrieve performance statistics: ' . $e->getMessage() . '</comment>');
        }
    }
}
