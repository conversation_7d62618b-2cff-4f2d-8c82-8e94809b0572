<?php
declare(strict_types=1);

namespace Lf\Franchise\Console\Command;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\Table;

/**
 * Console command to test franchise performance queries
 */
class TestFranchisePerformance extends Command
{
    private ResourceConnection $resourceConnection;

    /**
     * Test queries that should benefit from franchise indexes
     */
    private array $testQueries = [
        'orders_by_franchise' => [
            'name' => 'Orders by franchise',
            'query' => 'SELECT COUNT(*) FROM %s WHERE franchise_id = %d',
            'table' => 'sales_order'
        ],
        'orders_grid_by_franchise' => [
            'name' => 'Orders grid by franchise',
            'query' => 'SELECT COUNT(*) FROM %s WHERE franchise_id = %d',
            'table' => 'sales_order_grid'
        ],
        'invoices_by_franchise' => [
            'name' => 'Invoices by franchise (via JOIN)',
            'query' => 'SELECT COUNT(*) FROM %s si JOIN %s so ON si.order_id = so.entity_id WHERE so.franchise_id = %d',
            'table' => 'sales_invoice',
            'join_table' => 'sales_order'
        ],
        'creditmemos_by_franchise' => [
            'name' => 'Credit memos by franchise (via JOIN)',
            'query' => 'SELECT COUNT(*) FROM %s sc JOIN %s so ON sc.order_id = so.entity_id WHERE so.franchise_id = %d',
            'table' => 'sales_creditmemo',
            'join_table' => 'sales_order'
        ],
        'franchise_products' => [
            'name' => 'Franchise products',
            'query' => 'SELECT COUNT(*) FROM %s WHERE franchise_id = %d',
            'table' => 'franchise_product'
        ],
        'partenaires_by_franchise' => [
            'name' => 'Partenaires by franchise',
            'query' => 'SELECT COUNT(*) FROM %s WHERE franchise_id = %d',
            'table' => 'partenaire'
        ]
    ];

    public function __construct(ResourceConnection $resourceConnection)
    {
        $this->resourceConnection = $resourceConnection;
        parent::__construct();
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName('lf:franchise:test-performance')
            ->setDescription('Test performance of franchise-related queries')
            ->addOption(
                'franchise-id',
                'f',
                InputOption::VALUE_OPTIONAL,
                'Franchise ID to test (default: 1)',
                1
            )
            ->addOption(
                'iterations',
                'i',
                InputOption::VALUE_OPTIONAL,
                'Number of iterations per query (default: 5)',
                5
            )
            ->addOption(
                'explain',
                'e',
                InputOption::VALUE_NONE,
                'Show EXPLAIN output for queries'
            );
    }

    /**
     * @inheritdoc
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $franchiseId = (int) $input->getOption('franchise-id');
        $iterations = (int) $input->getOption('iterations');
        $showExplain = $input->getOption('explain');

        $output->writeln('<info>Testing franchise performance queries...</info>');
        $output->writeln(sprintf('Franchise ID: %d, Iterations: %d', $franchiseId, $iterations));
        $output->writeln('');

        $connection = $this->resourceConnection->getConnection();
        $results = [];

        foreach ($this->testQueries as $key => $queryInfo) {
            $tableName = $this->resourceConnection->getTableName($queryInfo['table']);
            
            // Check if table exists
            if (!$connection->isTableExists($tableName)) {
                $output->writeln(sprintf('<comment>Skipping %s - table does not exist</comment>', $queryInfo['name']));
                continue;
            }

            // Prepare query
            if (isset($queryInfo['join_table'])) {
                $joinTableName = $this->resourceConnection->getTableName($queryInfo['join_table']);
                $query = sprintf($queryInfo['query'], $tableName, $joinTableName, $franchiseId);
            } else {
                $query = sprintf($queryInfo['query'], $tableName, $franchiseId);
            }

            // Run performance test
            $times = [];
            for ($i = 0; $i < $iterations; $i++) {
                $startTime = microtime(true);
                try {
                    $result = $connection->fetchOne($query);
                    $endTime = microtime(true);
                    $times[] = ($endTime - $startTime) * 1000; // Convert to milliseconds
                } catch (\Exception $e) {
                    $output->writeln(sprintf('<error>Error in %s: %s</error>', $queryInfo['name'], $e->getMessage()));
                    continue 2;
                }
            }

            $avgTime = array_sum($times) / count($times);
            $minTime = min($times);
            $maxTime = max($times);

            $results[] = [
                'query' => $queryInfo['name'],
                'count' => $result ?? 0,
                'avg_time' => sprintf('%.2f ms', $avgTime),
                'min_time' => sprintf('%.2f ms', $minTime),
                'max_time' => sprintf('%.2f ms', $maxTime)
            ];

            // Show EXPLAIN if requested
            if ($showExplain) {
                $this->showExplain($output, $connection, $query, $queryInfo['name']);
            }
        }

        // Display results
        $table = new Table($output);
        $table->setHeaders(['Query', 'Count', 'Avg Time', 'Min Time', 'Max Time']);
        $table->setRows($results);
        $table->render();

        $output->writeln('');
        $output->writeln('<info>Performance test completed!</info>');
        
        // Show recommendations
        $this->showRecommendations($output, $results);

        return Cli::RETURN_SUCCESS;
    }

    /**
     * Show EXPLAIN output for a query
     */
    private function showExplain(OutputInterface $output, $connection, string $query, string $queryName): void
    {
        try {
            $explainQuery = 'EXPLAIN ' . $query;
            $explainResult = $connection->fetchAll($explainQuery);
            
            $output->writeln(sprintf('<info>EXPLAIN for %s:</info>', $queryName));
            
            if (!empty($explainResult)) {
                $table = new Table($output);
                $table->setHeaders(array_keys($explainResult[0]));
                $table->setRows($explainResult);
                $table->render();
            }
            
            $output->writeln('');
        } catch (\Exception $e) {
            $output->writeln(sprintf('<comment>Could not EXPLAIN query: %s</comment>', $e->getMessage()));
        }
    }

    /**
     * Show performance recommendations
     */
    private function showRecommendations(OutputInterface $output, array $results): void
    {
        $output->writeln('<info>Performance Recommendations:</info>');
        
        $slowQueries = array_filter($results, function($result) {
            $avgTime = (float) str_replace(' ms', '', $result['avg_time']);
            return $avgTime > 100; // Queries taking more than 100ms
        });

        if (empty($slowQueries)) {
            $output->writeln('<info>✓ All queries are performing well (< 100ms average)</info>');
        } else {
            $output->writeln('<comment>⚠ Slow queries detected (> 100ms average):</comment>');
            foreach ($slowQueries as $slowQuery) {
                $output->writeln(sprintf('  - %s: %s', $slowQuery['query'], $slowQuery['avg_time']));
            }
            $output->writeln('');
            $output->writeln('Consider:');
            $output->writeln('1. Running "php bin/magento setup:upgrade" to ensure all indexes are created');
            $output->writeln('2. Checking if franchise_id indexes exist with "php bin/magento lf:franchise:check-indexes"');
            $output->writeln('3. Analyzing query execution plans with --explain option');
        }
    }
}
