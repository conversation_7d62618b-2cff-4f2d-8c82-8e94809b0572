# Optimisation des performances - Index franchise_id

## Contexte

En mars 2025, lors de la création de nouveaux franchisés, nous avons constaté que le délai de chargement de la page **ventes > commandes** était très long pour toutes les franchises, même pour les nouvelles qui n'avaient encore que très peu de commandes.

Ce problème a été observé principalement sur :
- La cantine digitale
- Corporate events

Cédric avait alors créé en urgence un index sur la franchise pour la liste des commandes.

## Solution mise en place

Cette optimisation étend le travail initial en créant des index sur la colonne `franchise_id` (ou les colonnes de jointure équivalentes) pour toutes les listes qui filtrent par franchise.

### Tables optimisées

#### 1. **Commandes (sales_order)**
- **Index ajouté** : `IDX_SALES_ORDER_FRANCHISE_ID` sur `franchise_id`
- **Impact** : Améliore les requêtes de filtrage des commandes par franchise
- **Utilisation** : Page ventes > commandes

#### 2. **Grille des commandes (sales_order_grid)**
- **Index ajouté** : `IDX_SALES_ORDER_GRID_FRANCHISE_ID` sur `franchise_id`
- **Impact** : Améliore l'affichage de la grille des commandes
- **Utilisation** : Interface d'administration des commandes

#### 3. **Factures (sales_invoice)**
- **Index ajouté** : `IDX_SALES_INVOICE_ORDER_ID` sur `order_id`
- **Impact** : Améliore les jointures avec sales_order pour filtrer par franchise
- **Utilisation** : Page ventes > factures

#### 4. **Grille des factures (sales_invoice_grid)**
- **Index ajouté** : `IDX_SALES_INVOICE_GRID_ORDER_ID` sur `order_id`
- **Impact** : Améliore l'affichage de la grille des factures
- **Utilisation** : Interface d'administration des factures

#### 5. **Avoirs (sales_creditmemo)**
- **Index ajouté** : `IDX_SALES_CREDITMEMO_ORDER_ID` sur `order_id`
- **Impact** : Améliore les jointures avec sales_order pour filtrer par franchise
- **Utilisation** : Page ventes > avoirs

#### 6. **Grille des avoirs (sales_creditmemo_grid)**
- **Index ajouté** : `IDX_SALES_CREDITMEMO_GRID_ORDER_ID` sur `order_id`
- **Impact** : Améliore l'affichage de la grille des avoirs
- **Utilisation** : Interface d'administration des avoirs

#### 7. **Clients (customer_entity_text)**
- **Index ajouté** : `IDX_CUSTOMER_ENTITY_TEXT_ENTITY_ATTR` sur `entity_id, attribute_id`
- **Impact** : Améliore les requêtes FIND_IN_SET sur l'attribut `franchises_lies`
- **Utilisation** : Page clients > liste des clients

#### 8. **Produits franchise (franchise_product)**
- **Index ajouté** : `IDX_FRANCHISE_PRODUCT_FRANCHISE_ID` sur `franchise_id`
- **Impact** : Améliore le filtrage des produits par franchise
- **Utilisation** : Catalogue > produits franchises

#### 9. **Partenaires (partenaire)**
- **Index ajouté** : `IDX_PARTENAIRE_FRANCHISE_ID` sur `franchise_id`
- **Impact** : Améliore le filtrage des partenaires par franchise
- **Utilisation** : Filtrage indirect des commandes via partenaire

#### 10. **Contributions partenaires (eatlf_partenaire_contribution)**
- **Index ajouté** : `IDX_PARTENAIRE_CONTRIBUTION_PARTENAIRE_ID` sur `partenaire_id`
- **Impact** : Améliore les jointures pour les BAP
- **Utilisation** : Bons à payer

#### 11. **BAP (eatlf_bap)**
- **Index ajouté** : `IDX_EATLF_BAP_CONTRIBUTION_ID` sur `contribution_id`
- **Impact** : Améliore le filtrage des BAP par franchise via contribution
- **Utilisation** : Page bons à payer

## Fichiers modifiés

### 1. Patch de schéma
- **Fichier** : `src/app/code/Lf/Franchise/Setup/Patch/Schema/AddFranchiseIndexes.php`
- **Rôle** : Applique les index de manière sécurisée (vérifie l'existence des tables/colonnes)

### 2. Schéma déclaratif
- **Fichier** : `src/app/code/Lf/Franchise/etc/db_schema.xml`
- **Rôle** : Déclare les index dans le schéma Magento

### 3. Whitelist du schéma
- **Fichier** : `src/app/code/Lf/Franchise/etc/db_schema_whitelist.json`
- **Rôle** : Autorise les nouveaux index dans le système de schéma déclaratif

## Installation

### 1. Appliquer les patches
```bash
php bin/magento setup:upgrade
```

### 2. Vérifier les index (optionnel)
```sql
-- Vérifier les index sur sales_order
SHOW INDEX FROM sales_order WHERE Key_name LIKE '%FRANCHISE%';

-- Vérifier les index sur franchise_product
SHOW INDEX FROM franchise_product WHERE Key_name LIKE '%FRANCHISE%';

-- Vérifier les index sur partenaire
SHOW INDEX FROM partenaire WHERE Key_name LIKE '%FRANCHISE%';
```

## Impact attendu

- **Réduction significative** du temps de chargement des pages de listing
- **Amélioration des performances** pour toutes les franchises, y compris les nouvelles
- **Optimisation des requêtes** de filtrage par franchise dans l'administration

## Monitoring

Après déploiement, surveiller :
- Les temps de réponse des pages d'administration
- Les requêtes lentes dans les logs MySQL
- L'utilisation des nouveaux index via `EXPLAIN` sur les requêtes critiques

## Index supplémentaires identifiés

Si d'autres problèmes de performance sont identifiés, considérer l'ajout d'index sur :
- `quote_shipping_datas.franchise_id` (si cette colonne existe)
- Index composites pour des requêtes spécifiques (ex: `franchise_id, created_at`)
